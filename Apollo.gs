// =============================================================
// Apollo.io Enhanced Company & C‑Level Executive Data Fetcher v5.0.0
// Enhanced with social media metrics, robust logging, and data enrichment
// =============================================================

// ===================== CONFIGURATION ==========================
const CONFIG = {
  BATCH_SIZE: 8,                   // rows per execution (reduced for enhanced processing)
  MAX_RETRIES: 3,                  // for 5xx / 429
  RETRY_DELAY: 1000,               // base ms (exp back‑off)
  RATE_LIMIT_PER_MINUTE: 180,      // Apollo hard cap – conservative limit
  SOCIAL_MEDIA_TIMEOUT: 5000,      // timeout for social media API calls
  LOG_LEVEL: 'INFO',               // DEBUG, INFO, WARN, ERROR
  ENABLE_SOCIAL_METRICS: true,     // enable Twitter/social media metrics
  ENABLE_DATA_ENRICHMENT: true     // enable additional data enrichment
};

const STATUS = {                   // one source of truth
  PENDING:     '',
  PROCESSING:  'Processing',
  COMPLETE:    'Completed',
  ERROR:       'Error',
  ENRICHING:   'Enriching'
};

// ===================== LOGGING SYSTEM ==========================
class Logger {
  constructor(level = CONFIG.LOG_LEVEL) {
    this.levels = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
    this.level = this.levels[level] || 1;
    this.logSheet = null;
  }

  _getLogSheet() {
    if (!this.logSheet) {
      const ss = SpreadsheetApp.getActiveSpreadsheet();
      this.logSheet = ss.getSheetByName('🔍 Debug Logs');
      if (!this.logSheet) {
        this.logSheet = ss.insertSheet('🔍 Debug Logs');
        this.logSheet.appendRow(['Timestamp', 'Level', 'Component', 'Message', 'Data']);
        this.logSheet.getRange(1, 1, 1, 5).setFontWeight('bold').setBackground('#34495e').setFontColor('#fff');
      }
    }
    return this.logSheet;
  }

  _log(level, component, message, data = null) {
    if (this.levels[level] < this.level) return;

    console.log(`[${level}] ${component}: ${message}`, data || '');

    try {
      const sheet = this._getLogSheet();
      const timestamp = new Date().toLocaleString();
      const dataStr = data ? JSON.stringify(data).substring(0, 500) : '';
      sheet.appendRow([timestamp, level, component, message, dataStr]);

      // Keep only last 1000 log entries
      if (sheet.getLastRow() > 1001) {
        sheet.deleteRows(2, sheet.getLastRow() - 1001);
      }
    } catch (err) {
      console.error('Logging failed:', err);
    }
  }

  debug(component, message, data) { this._log('DEBUG', component, message, data); }
  info(component, message, data) { this._log('INFO', component, message, data); }
  warn(component, message, data) { this._log('WARN', component, message, data); }
  error(component, message, data) { this._log('ERROR', component, message, data); }
}

const logger = new Logger();

// ===================== TWITTER API CLIENT ==========================
class TwitterAPIClient {
  constructor() {
    this.baseUrl = 'https://api.twitterapi.io/twitter/user/info';
    this.apiKey = PropertiesService.getScriptProperties().getProperty('TWITTER_API_KEY');
    this.enabled = !!this.apiKey && CONFIG.ENABLE_SOCIAL_METRICS;

    if (this.enabled) {
      logger.info('TWITTER_CLIENT', 'TwitterAPI.io client initialized');
    } else {
      logger.info('TWITTER_CLIENT', 'TwitterAPI.io client disabled (no API key or feature disabled)');
    }
  }

  // Get Twitter user info including follower count
  getUserInfo(username) {
    if (!this.enabled) {
      logger.debug('TWITTER_CLIENT', 'Twitter API disabled, skipping user lookup');
      return null;
    }

    if (!username) {
      logger.warn('TWITTER_CLIENT', 'No username provided for Twitter lookup');
      return null;
    }

    // Clean username (remove @ if present)
    const cleanUsername = username.replace('@', '').trim();

    try {
      logger.debug('TWITTER_CLIENT', `Fetching Twitter data for: ${cleanUsername}`);

      const url = `${this.baseUrl}?userName=${encodeURIComponent(cleanUsername)}`;
      const options = {
        method: 'GET',
        headers: {
          'X-API-Key': this.apiKey,
          'Content-Type': 'application/json'
        },
        muteHttpExceptions: true
      };

      const response = UrlFetchApp.fetch(url, options);
      const statusCode = response.getResponseCode();

      if (statusCode !== 200) {
        logger.warn('TWITTER_CLIENT', `Twitter API error for ${cleanUsername}`, {
          statusCode,
          response: response.getContentText()
        });
        return null;
      }

      const data = JSON.parse(response.getContentText());

      if (data.status !== 'success' || !data.data) {
        logger.warn('TWITTER_CLIENT', `Invalid Twitter API response for ${cleanUsername}`, data);
        return null;
      }

      const userInfo = {
        username: data.data.userName,
        name: data.data.name,
        followers: data.data.followers || 0,
        following: data.data.following || 0,
        tweets: data.data.statusesCount || 0,
        verified: data.data.isBlueVerified || false,
        description: data.data.description || '',
        location: data.data.location || '',
        createdAt: data.data.createdAt || '',
        profilePicture: data.data.profilePicture || '',
        url: data.data.url || `https://twitter.com/${cleanUsername}`
      };

      logger.info('TWITTER_CLIENT', `Successfully fetched Twitter data for ${cleanUsername}`, {
        followers: userInfo.followers,
        verified: userInfo.verified
      });

      return userInfo;

    } catch (error) {
      logger.error('TWITTER_CLIENT', `Failed to fetch Twitter data for ${cleanUsername}`, error);
      return null;
    }
  }

  // Extract username from Twitter URL
  extractUsernameFromUrl(twitterUrl) {
    if (!twitterUrl) return null;

    const match = twitterUrl.match(/(?:twitter\.com|x\.com)\/([^\/\?]+)/);
    return match ? match[1] : null;
  }

  // Format follower count for display
  formatFollowerCount(count) {
    if (!count || count === 0) return '0';
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  }
}

const twitterClient = new TwitterAPIClient();

// =============================================================
//                 ———  MENU  ———
// =============================================================
function onOpen() {
  SpreadsheetApp.getUi().createMenu('🚀 Apollo Enhanced Fetcher')
    .addItem('📋 Setup Sheet',          'setupSheet')
    .addItem('🔍 Fetch Company Data',   'fetchCompanyData')
    .addSeparator()
    .addItem('🔑 Set Apollo API Key',   'setApolloApiKey')
    .addItem('� Set Social Media APIs', 'setSocialMediaKeys')
    .addItem('�🛑 Stop All Jobs',        'stopAllJobs')
    .addSeparator()
    .addItem('📊 View Debug Logs',      'viewDebugLogs')
    .addItem('🧹 Clear Logs',           'clearLogs')
    .addItem('❓ Help',                 'showHelp')
    .addToUi();
}

// ================== API KEY MANAGEMENT ====================
function setApolloApiKey() {
  const ui  = SpreadsheetApp.getUi();
  const res = ui.prompt('Apollo API Key', 'Paste your Apollo.io key:', ui.ButtonSet.OK_CANCEL);
  if (res.getSelectedButton() !== ui.Button.OK) return;
  const key = res.getResponseText().trim();
  if (!key) return;
  PropertiesService.getScriptProperties().setProperty('APOLLO_API_KEY', key);
  logger.info('API_SETUP', 'Apollo API key configured successfully');
  ui.alert('Apollo API key saved ✔️');
}

function setSocialMediaKeys() {
  const ui = SpreadsheetApp.getUi();
  const res = ui.prompt(
    'TwitterAPI.io Setup',
    'Enter your TwitterAPI.io API key (get one at https://twitterapi.io):\n\nPricing: $0.18 per 1K profiles\nThis will add Twitter follower counts to your company data.',
    ui.ButtonSet.OK_CANCEL
  );

  if (res.getSelectedButton() !== ui.Button.OK) return;
  const key = res.getResponseText().trim();

  if (!key) {
    ui.alert('No API key entered. Twitter follower data will be skipped.');
    return;
  }

  PropertiesService.getScriptProperties().setProperty('TWITTER_API_KEY', key);
  logger.info('API_SETUP', 'TwitterAPI.io key configured successfully');
  ui.alert('TwitterAPI.io key saved ✔️\n\nNow your company data will include Twitter follower counts!');
}

// ==================== PUBLIC ENTRY ============================
function fetchCompanyData() {
  const lock = LockService.getScriptLock();
  if (!lock.tryLock(0)) { toast_('Fetcher is already running.'); return; }
  try {
    const apiKey = PropertiesService.getScriptProperties().getProperty('APOLLO_API_KEY');
    if (!apiKey) { toast_('Missing API key – run "Set / Update API Key" first.'); return; }
    stopAllJobs();                       // kill orphan triggers
    PropertiesService.getScriptProperties().deleteProperty('rowPointer');
    processCompanies_();                 // start immediately
  } finally { lock.releaseLock(); }
}

// =============== BATCH PROCESSOR (PRIVATE) ====================
function processCompanies_() {
  const lock = LockService.getScriptLock();
  if (!lock.tryLock(0)) return;          // another batch is running
  try {
    const ss     = SpreadsheetApp.getActiveSpreadsheet();
    const sheet  = ss.getActiveSheet();
    const rows   = sheet.getLastRow() - 1;  // data rows after header
    if (rows <= 0) { toast_('No company domains to process.'); return; }

    const data   = sheet.getRange(2, 1, rows, 5).getValues();
    const done   = getProcessedDomains_();

    const ptr = Number(PropertiesService.getScriptProperties().getProperty('rowPointer') || 0);
    if (ptr >= data.length) { toast_('All companies processed ✔️'); return; }

    const batch  = data.slice(ptr, ptr + CONFIG.BATCH_SIZE);
    const apollo = new EnhancedApolloClient();

    batch.forEach((row, i) => {
      const [domain, status] = row;
      if (!domain) return;

      // Skip if already complete or summary already has domain
      if (status === STATUS.COMPLETE || done.has(domain)) {
        data[ptr+i][1] = STATUS.COMPLETE;
        return;
      }

      logger.info('BATCH_PROCESSOR', `Processing domain: ${domain}`);
      data[ptr+i][1] = STATUS.PROCESSING;

      try {
        // Step 1: Enrich company data with comprehensive Apollo information
        data[ptr+i][1] = STATUS.ENRICHING;
        const company = apollo.enrichCompany(domain);

        // Step 2: Search for executives with enhanced data
        const executives = apollo.searchExecutives(domain);

        // Step 3: Enrich individual executives (optional, for high-value prospects)
        const enrichedExecutives = [];
        for (const exec of executives.slice(0, 5)) { // Limit to top 5 to avoid rate limits
          try {
            const enrichedExec = apollo.enrichPerson(exec);
            enrichedExecutives.push(enrichedExec);
          } catch (enrichErr) {
            logger.warn('BATCH_PROCESSOR', `Executive enrichment failed for ${exec.name}`, enrichErr);
            enrichedExecutives.push(exec); // Use original data
          }
        }

        // Add remaining executives without enrichment
        enrichedExecutives.push(...executives.slice(5));

        // Step 4: Enrich with Twitter follower data
        let twitterData = null;
        if (company.twitter_url && CONFIG.ENABLE_SOCIAL_METRICS) {
          const twitterUsername = twitterClient.extractUsernameFromUrl(company.twitter_url);
          if (twitterUsername) {
            twitterData = twitterClient.getUserInfo(twitterUsername);
            if (twitterData) {
              logger.info('BATCH_PROCESSOR', `Twitter data enriched for ${domain}`, {
                username: twitterData.username,
                followers: twitterData.followers
              });
            }
          }
        }

        // Step 5: Create comprehensive company sheet with all Apollo + Twitter data
        const sheetName = createOrUpdateCompanySheet_(domain, company, enrichedExecutives, twitterData);

        // Step 6: Update summary with enhanced metrics including Twitter data
        updateSummary_(domain, company, enrichedExecutives.length, sheetName, twitterData);
        done.add(domain);

        data[ptr+i][1] = STATUS.COMPLETE;

        // Enhanced results summary with Twitter metrics
        const metrics = [];
        if (company.estimated_num_employees) metrics.push(`${company.estimated_num_employees} employees`);
        if (enrichedExecutives.length) metrics.push(`${enrichedExecutives.length} executives`);
        if (twitterData?.followers) metrics.push(`${twitterClient.formatFollowerCount(twitterData.followers)} Twitter followers`);
        if (company.total_funding) metrics.push(`${company.total_funding_printed || company.total_funding} funding`);
        if (company.technology_names?.length) metrics.push(`${company.technology_names.length} technologies`);

        data[ptr+i][4] = metrics.join(' • ') || `${enrichedExecutives.length} executives found`;

        logger.info('BATCH_PROCESSOR', `Successfully processed ${domain}`, {
          executives: enrichedExecutives.length,
          funding: company.total_funding_printed,
          employees: company.estimated_num_employees,
          technologies: company.technology_names?.length || 0,
          twitterFollowers: twitterData?.followers || 0,
          twitterVerified: twitterData?.verified || false
        });

      } catch (err) {
        data[ptr+i][1] = STATUS.ERROR;
        data[ptr+i][2] = (err && err.message) || String(err);
        logger.error('BATCH_PROCESSOR', `Failed to process ${domain}`, err);
      }
    });

    sheet.getRange(2, 1, rows, 5).setValues(data);

    if (ptr + CONFIG.BATCH_SIZE < data.length) {
      PropertiesService.getScriptProperties().setProperty('rowPointer', ptr + CONFIG.BATCH_SIZE);
      ScriptApp.newTrigger('processCompanies_').timeBased().after(30000).create();
    } else {
      PropertiesService.getScriptProperties().deleteProperty('rowPointer');
      toast_('All companies processed ✔️');
    }
  } finally { lock.releaseLock(); }
}

// ===================== ENHANCED APOLLO CLIENT ==========================
class EnhancedApolloClient {
  constructor() {
    this.baseUrl = 'https://api.apollo.io/api/v1/';
    this.apiKey  = PropertiesService.getScriptProperties().getProperty('APOLLO_API_KEY');
    this.rateLimiter = new ApolloRateLimiter();
    logger.info('APOLLO_CLIENT', 'Apollo client initialized');
  }

  // Enhanced company enrichment with comprehensive data extraction
  enrichCompany(domain) {
    logger.info('APOLLO_CLIENT', `Enriching company data for domain: ${domain}`);
    try {
      const resp = this.rateLimiter.makeRequest(this.baseUrl+'organizations/enrich', this._opts_({ domain }));
      this._check_(resp);
      const data = JSON.parse(resp.getContentText());
      const org = data.organization || {};

      logger.info('APOLLO_CLIENT', `Company enrichment successful for ${domain}`, {
        name: org.name,
        employees: org.estimated_num_employees,
        industry: org.industry,
        hasTwitter: !!org.twitter_url,
        hasLinkedIn: !!org.linkedin_url,
        hasFunding: !!org.total_funding
      });

      return org;
    } catch (error) {
      logger.error('APOLLO_CLIENT', `Company enrichment failed for ${domain}`, error);
      throw error;
    }
  }

  // Enhanced executive search with detailed person enrichment
  searchExecutives(domain) {
    logger.info('APOLLO_CLIENT', `Searching executives for domain: ${domain}`);
    const execs = [];
    let page = 1, total = 1;

    try {
      while (page <= total) {
        logger.debug('APOLLO_CLIENT', `Fetching executives page ${page} for ${domain}`);
        const resp = this.rateLimiter.makeRequest(
          this.baseUrl+'mixed_people/search',
          this._opts_({
            q_organization_domains: domain,
            person_seniorities: ['c_suite','owner','founder','vp'], // Include VPs for more data
            page,
            per_page: 100,
            reveal_personal_emails: true,  // Get more contact info
            reveal_phone_number: true
          })
        );
        this._check_(resp);
        const j = JSON.parse(resp.getContentText());

        if (Array.isArray(j.people)) {
          const filteredExecs = j.people.filter(isCLevel_);
          execs.push(...filteredExecs);
          logger.debug('APOLLO_CLIENT', `Found ${filteredExecs.length} executives on page ${page}`);
        }

        total = j.pagination?.total_pages || 1;
        page++;
      }

      logger.info('APOLLO_CLIENT', `Executive search completed for ${domain}`, {
        totalExecutives: execs.length,
        totalPages: total - 1
      });

      return execs;
    } catch (error) {
      logger.error('APOLLO_CLIENT', `Executive search failed for ${domain}`, error);
      throw error;
    }
  }

  // NEW: Enhanced person enrichment for individual executives
  enrichPerson(person) {
    if (!person.email && !person.linkedin_url) {
      logger.warn('APOLLO_CLIENT', 'Insufficient data for person enrichment', { name: person.name });
      return person;
    }

    try {
      logger.debug('APOLLO_CLIENT', `Enriching person: ${person.name || person.first_name + ' ' + person.last_name}`);
      const payload = {
        reveal_personal_emails: true,
        reveal_phone_number: true
      };

      if (person.email) payload.email = person.email;
      if (person.linkedin_url) payload.linkedin_url = person.linkedin_url;
      if (person.first_name) payload.first_name = person.first_name;
      if (person.last_name) payload.last_name = person.last_name;

      const resp = this.rateLimiter.makeRequest(this.baseUrl+'people/match', this._opts_(payload));
      this._check_(resp);
      const enrichedData = JSON.parse(resp.getContentText());

      // Merge enriched data with original person data
      return { ...person, ...enrichedData.person };
    } catch (error) {
      logger.warn('APOLLO_CLIENT', `Person enrichment failed for ${person.name}`, error);
      return person; // Return original data if enrichment fails
    }
  }

  // NEW: Get company technologies and tools
  getCompanyTechnologies(organizationId) {
    if (!organizationId) return [];

    try {
      logger.debug('APOLLO_CLIENT', `Fetching technologies for organization: ${organizationId}`);
      // Note: This might require a different endpoint or be included in organization enrichment
      // For now, we'll extract from the main organization data
      return [];
    } catch (error) {
      logger.warn('APOLLO_CLIENT', `Technology fetch failed for org ${organizationId}`, error);
      return [];
    }
  }

  _opts_(payload) {
    return {
      method: 'post',
      payload: JSON.stringify(payload),
      headers: {
        'X-Api-Key': this.apiKey,
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      },
      muteHttpExceptions: true
    };
  }

  _check_(r) {
    const c = r.getResponseCode();
    if (c < 200 || c >= 300) {
      const errorMsg = `Apollo API ${c}: ${r.getContentText()}`;
      logger.error('APOLLO_CLIENT', 'API request failed', { statusCode: c, response: r.getContentText() });
      throw new Error(errorMsg);
    }
  }
}

// ====================== RATE LIMITER ==========================
class ApolloRateLimiter {
  constructor() {
    this.key='apollo_rl';
    const c = CacheService.getScriptCache().get(this.key);
    if(c){ const { windowStart, requestCount } = JSON.parse(c); this.windowStart=windowStart; this.requestCount=requestCount; }
    else { this.windowStart=Date.now(); this.requestCount=0; }
    this.windowDuration=60000; this.maxRequests=CONFIG.RATE_LIMIT_PER_MINUTE;
  }
  makeRequest(url,opt,a=0){ this._gate_(); const r=UrlFetchApp.fetch(url,opt); this.requestCount++; this._save_(); const code=r.getResponseCode(); if([429,500,502,503,504].includes(code)){ if(a>=CONFIG.MAX_RETRIES) return r; Utilities.sleep(Math.min(CONFIG.RETRY_DELAY*2**a,30000)); return this.makeRequest(url,opt,a+1);} return r; }
  _gate_(){ const n=Date.now(); if(n-this.windowStart>=this.windowDuration){ this.windowStart=n; this.requestCount=0;} if(this.requestCount>=this.maxRequests){ Utilities.sleep(this.windowDuration-(n-this.windowStart)); this.windowStart=Date.now(); this.requestCount=0; } }
  _save_(){ CacheService.getScriptCache().put(this.key,JSON.stringify({windowStart:this.windowStart,requestCount:this.requestCount}),60);} }

// ================= EXECUTIVE FILTER HELPER ====================
function isCLevel_(p){ const t=(p.title||'').toLowerCase(); const s=(p.seniority||'').toLowerCase(); const chief=['chief','ceo','cfo','coo','cto','cmo','cro','cpo','chro','cio','cso','cdo','clo'].some(k=>t.includes(k)); const founder=t.includes('founder')||t.includes('owner')||['founder','owner'].includes(s); const vp=t.includes('vice president')||/\bvp\b/.test(t); const pres=t.includes('president')&&!chief; return (chief||founder)&&!vp&&!pres; }

// ============= ENHANCED SHEET CREATION WITH COMPREHENSIVE APOLLO + TWITTER DATA ===============
function createOrUpdateCompanySheet_(domain, company, executives, twitterData = null){
  logger.info('SHEET_CREATION', `Creating comprehensive sheet for ${domain}`);
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheetName = createValidSheetName_(company.name || domain, ss);
  let sheet = ss.getSheetByName(sheetName);
  if (!sheet) sheet = ss.insertSheet(sheetName);
  sheet.clear();

  const rows = [];

  // ========== COMPANY OVERVIEW ==========
  rows.push(['🏢 COMPANY OVERVIEW']);
  rows.push(['Field', 'Value', 'Additional Info']);
  rows.push(['Company Name', company.name || '', '']);
  rows.push(['Domain', domain, '']);
  rows.push(['Industry', company.industry || '', company.secondary_industries?.join(', ') || '']);
  rows.push(['Employee Count', company.estimated_num_employees || '', formatEmployeeRange_(company.estimated_num_employees)]);
  rows.push(['Founded Year', company.founded_year || '', '']);
  rows.push(['Headquarters', formatHeadquarters_(company), company.raw_address || '']);
  rows.push(['Phone', company.phone || company.primary_phone?.number || '', company.sanitized_phone || '']);
  rows.push(['Description', company.short_description || company.seo_description || '', '']);
  rows.push(['Keywords', (company.keywords || []).slice(0, 10).join(', '), `Total: ${(company.keywords || []).length} keywords`]);
  rows.push(['']);

  // ========== FINANCIAL & BUSINESS DATA ==========
  if (company.total_funding || company.annual_revenue || company.publicly_traded_symbol) {
    rows.push(['💰 FINANCIAL INFORMATION']);
    rows.push(['Metric', 'Value', 'Details']);
    if (company.total_funding) {
      rows.push(['Total Funding', company.total_funding_printed || company.total_funding,
                 `Latest: ${company.latest_funding_stage || ''} (${company.latest_funding_round_date || ''})`]);
    }
    if (company.annual_revenue) {
      rows.push(['Annual Revenue', company.annual_revenue_printed || company.annual_revenue, '']);
    }
    if (company.publicly_traded_symbol) {
      rows.push(['Stock Symbol', company.publicly_traded_symbol, company.publicly_traded_exchange || '']);
    }
    if (company.alexa_ranking) {
      rows.push(['Alexa Ranking', company.alexa_ranking, '']);
    }
    rows.push(['']);
  }

  // ========== SOCIAL MEDIA & WEB PRESENCE ==========
  rows.push(['🌐 DIGITAL PRESENCE & SOCIAL METRICS']);
  rows.push(['Platform', 'URL', 'Metrics']);
  const socials = extractSocialLinks_(company);
  rows.push(['Website', makeClickableLink_(company.website_url || ''), company.blog_url ? 'Has Blog' : '']);
  rows.push(['LinkedIn', makeClickableLink_(company.linkedin_url || socials.linkedin), company.linkedin_uid || '']);

  // Enhanced Twitter row with follower data
  let twitterMetrics = extractTwitterHandle_(company.twitter_url);
  if (twitterData) {
    twitterMetrics = `${twitterData.username} • ${twitterClient.formatFollowerCount(twitterData.followers)} followers`;
    if (twitterData.verified) twitterMetrics += ' • Verified ✓';
  }
  rows.push(['Twitter', makeClickableLink_(company.twitter_url || socials.twitter), twitterMetrics]);

  rows.push(['Facebook', makeClickableLink_(company.facebook_url || socials.facebook), '']);
  rows.push(['Instagram', makeClickableLink_(socials.instagram), '']);
  rows.push(['YouTube', makeClickableLink_(socials.youtube), '']);
  if (company.angellist_url) rows.push(['AngelList', makeClickableLink_(company.angellist_url), '']);
  if (company.crunchbase_url) rows.push(['Crunchbase', makeClickableLink_(company.crunchbase_url), '']);
  socials.others.forEach((u, i) => rows.push([`Other ${i + 1}`, makeClickableLink_(u), '']));

  // Add detailed Twitter analytics if available
  if (twitterData) {
    rows.push(['']);
    rows.push(['🐦 TWITTER ANALYTICS']);
    rows.push(['Metric', 'Value', 'Details']);
    rows.push(['Followers', twitterClient.formatFollowerCount(twitterData.followers), twitterData.followers.toLocaleString()]);
    rows.push(['Following', twitterClient.formatFollowerCount(twitterData.following), twitterData.following.toLocaleString()]);
    rows.push(['Tweets', twitterClient.formatFollowerCount(twitterData.tweets), twitterData.tweets.toLocaleString()]);
    rows.push(['Verified Status', twitterData.verified ? 'Verified ✓' : 'Not Verified', '']);
    if (twitterData.location) rows.push(['Location', twitterData.location, '']);
    if (twitterData.createdAt) {
      const createdDate = new Date(twitterData.createdAt);
      const accountAge = new Date().getFullYear() - createdDate.getFullYear();
      rows.push(['Account Created', createdDate.toLocaleDateString(), `${accountAge} years old`]);
    }

    // Calculate engagement metrics
    if (twitterData.followers > 0 && twitterData.tweets > 0) {
      const avgEngagement = (twitterData.tweets / twitterData.followers * 100).toFixed(2);
      rows.push(['Engagement Ratio', `${avgEngagement}%`, 'Tweets per follower']);
    }
  }

  rows.push(['']);

  // ========== TECHNOLOGY STACK ==========
  if (company.technology_names && company.technology_names.length > 0) {
    rows.push(['⚙️ TECHNOLOGY STACK']);
    rows.push(['Technology', 'Category', 'Type']);

    // Group technologies by category if current_technologies is available
    if (company.current_technologies && company.current_technologies.length > 0) {
      company.current_technologies.forEach(tech => {
        rows.push([tech.name || '', tech.category || '', 'Current']);
      });
    } else {
      // Fallback to technology_names list
      company.technology_names.slice(0, 20).forEach(tech => {
        rows.push([tech, '', 'Detected']);
      });
    }

    if (company.technology_names.length > 20) {
      rows.push([`... and ${company.technology_names.length - 20} more technologies`, '', '']);
    }
    rows.push(['']);
  }

  // ========== FUNDING HISTORY ==========
  if (company.funding_events && company.funding_events.length > 0) {
    rows.push(['💸 FUNDING HISTORY']);
    rows.push(['Date', 'Round Type', 'Amount', 'Investors']);
    company.funding_events.forEach(event => {
      rows.push([
        event.date ? new Date(event.date).toLocaleDateString() : '',
        event.type || '',
        event.amount ? `${event.currency || '$'}${event.amount}` : '',
        event.investors || ''
      ]);
    });
    rows.push(['']);
  }

  // ========== DEPARTMENTAL BREAKDOWN ==========
  if (company.departmental_head_count) {
    rows.push(['👥 DEPARTMENTAL BREAKDOWN']);
    rows.push(['Department', 'Head Count', 'Percentage']);
    const totalEmployees = company.estimated_num_employees || 1;
    Object.entries(company.departmental_head_count)
      .filter(([dept, count]) => count > 0)
      .sort(([,a], [,b]) => b - a)
      .forEach(([dept, count]) => {
        const percentage = ((count / totalEmployees) * 100).toFixed(1);
        rows.push([dept.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), count, `${percentage}%`]);
      });
    rows.push(['']);
  }

  // ========== EXECUTIVES SECTION ==========
  rows.push([`👔 C-LEVEL EXECUTIVES (${executives.length} found)`]);
  rows.push(['Name', 'Title', 'Email', 'LinkedIn', 'Phone', 'Location', 'Experience']);

  const prio = {ceo:1, coo:2, cfo:3, cto:4, cmo:5, cro:6, founder:7, owner:8, president:9};
  executives.sort((a, b) => {
    const ak = Object.keys(prio).find(k => (a.title || '').toLowerCase().includes(k)) || 'zz';
    const bk = Object.keys(prio).find(k => (b.title || '').toLowerCase().includes(k)) || 'zz';
    return (prio[ak] || 99) - (prio[bk] || 99);
  });

  executives.forEach(e => {
    const experience = e.employment_history ?
      `${e.employment_history.length} positions` : '';
    rows.push([
      e.name || `${e.first_name || ''} ${e.last_name || ''}`.trim(),
      e.title || '',
      makeClickableEmail_(e.email),
      makeClickableLink_(e.linkedin_url),
      (e.phone_numbers || []).map(p => p.raw_number).join(', '),
      [e.city, e.state, e.country].filter(Boolean).join(', '),
      experience
    ]);
  });

  // Apply data to sheet with proper column count
  const maxCols = Math.max(...rows.map(r => r.length));
  const normalizedRows = rows.map(r => {
    while (r.length < maxCols) r.push('');
    return r;
  });

  sheet.getRange(1, 1, normalizedRows.length, maxCols).setValues(normalizedRows);

  // Set column widths
  const colWidths = [150, 200, 200, 200, 150, 150, 120];
  colWidths.forEach((w, i) => {
    if (i < maxCols) sheet.setColumnWidth(i + 1, w);
  });

  // Format headers
  sheet.getRange(1, 1).setFontSize(14).setFontWeight('bold');

  logger.info('SHEET_CREATION', `Sheet created successfully for ${domain}`, {
    totalRows: normalizedRows.length,
    totalCols: maxCols,
    executiveCount: executives.length
  });

  return sheetName;
}

function updateSummary_(domain, company, execCount, sheetName, twitterData = null) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  let summary = ss.getSheetByName('📊 Summary');

  // Create summary sheet with enhanced headers including Twitter metrics
  if (!summary) {
    summary = ss.insertSheet('📊 Summary');
    summary.appendRow([
      'Company', 'Domain', 'C‑level Count', 'Industry', 'Employees',
      'Headquarters', 'Twitter Followers', 'Funding', 'Last Updated', 'Sheet Link'
    ]);
    summary.getRange(1, 1, 1, 10).setFontWeight('bold').setBackground('#1a73e8').setFontColor('#fff');
  }

  const last = summary.getLastRow();
  let domains = [];
  if (last >= 2) domains = summary.getRange(2, 2, last - 1, 1).getValues().flat();
  const idx = domains.findIndex(d => d === domain);
  const linkSheet = ss.getSheetByName(sheetName);
  const sheetLink = linkSheet ? `=HYPERLINK("#gid=${linkSheet.getSheetId()}","View →")` : '';

  // Enhanced row data with Twitter metrics
  let twitterFollowers = 'N/A';
  if (twitterData && twitterData.followers) {
    twitterFollowers = twitterClient.formatFollowerCount(twitterData.followers);
    if (twitterData.verified) twitterFollowers += ' ✓';
  }

  const fundingInfo = company.total_funding_printed ||
                     (company.total_funding ? formatFundingAmount_(company.total_funding) : '');

  const row = [
    company.name || domain,
    domain,
    execCount,
    company.industry || '',
    company.estimated_num_employees || '',
    formatHeadquarters_(company),
    twitterFollowers,
    fundingInfo,
    new Date().toLocaleString(),
    sheetLink
  ];

  if (idx >= 0) summary.getRange(idx + 2, 1, 1, row.length).setValues([row]);
  else summary.appendRow(row);

  logger.info('SUMMARY_UPDATE', `Summary updated for ${domain}`, {
    twitterFollowers: twitterData?.followers || 0,
    funding: fundingInfo
  });
}

// =============== ENHANCED UTIL & HELPER FUNCTIONS =====================
function getProcessedDomains_() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const s = ss.getSheetByName('📊 Summary');
  if (!s || s.getLastRow() < 2) return new Set();
  return new Set(s.getRange(2, 2, s.getLastRow() - 1, 1).getValues().flat().filter(Boolean));
}

function stopAllJobs() {
  const triggers = ScriptApp.getProjectTriggers().filter(t => t.getHandlerFunction() === 'processCompanies_');
  triggers.forEach(t => ScriptApp.deleteTrigger(t));
  logger.info('JOB_CONTROL', `Stopped ${triggers.length} running jobs`);
}

function createValidSheetName_(name, ss) {
  let base = name.replace(/[*/\[\]:?]/g, '').substring(0, 50).trim() || 'Sheet';
  let finalName = base, idx = 1;
  while (ss.getSheetByName(finalName)) {
    finalName = `${base} (${idx++})`;
  }
  return finalName;
}

function extractSocialLinks_(c) {
  const r = {linkedin: '', twitter: '', facebook: '', instagram: '', youtube: '', others: []};
  const urls = [c.linkedin_url, c.twitter_url, c.facebook_url, ...(c.social_urls || [])].filter(Boolean);
  urls.forEach(u => {
    if (u.includes('linkedin.com')) r.linkedin = u;
    else if (u.includes('twitter.com') || u.includes('x.com')) r.twitter = u;
    else if (u.includes('facebook.com')) r.facebook = u;
    else if (u.includes('instagram.com')) r.instagram = u;
    else if (u.includes('youtube.com')) r.youtube = u;
    else r.others.push(u);
  });
  return r;
}

function formatHeadquarters_(c) {
  if (c.primary_address) {
    const a = c.primary_address;
    return [a.city, a.state, a.country].filter(Boolean).join(', ');
  }
  return [c.city, c.state, c.country].filter(Boolean).join(', ');
}

function makeClickableLink_(url) {
  if (!url) return '';
  if (!/^https?:\/\//i.test(url)) url = 'https://' + url;
  return `=HYPERLINK("${url}","View →")`;
}

function makeClickableEmail_(email) {
  if (!email) return '';
  return `=HYPERLINK("mailto:${email}","${email}")`;
}

function toast_(msg) {
  SpreadsheetApp.getActiveSpreadsheet().toast(msg, 'Apollo Enhanced');
  logger.info('UI', `Toast message: ${msg}`);
}

// NEW: Enhanced helper functions for comprehensive data display
function formatEmployeeRange_(count) {
  if (!count) return '';
  if (count < 10) return 'Startup (1-10)';
  if (count < 50) return 'Small (11-50)';
  if (count < 200) return 'Medium (51-200)';
  if (count < 1000) return 'Large (201-1000)';
  return 'Enterprise (1000+)';
}

function extractTwitterHandle_(twitterUrl) {
  if (!twitterUrl) return '';
  const match = twitterUrl.match(/(?:twitter\.com|x\.com)\/([^\/\?]+)/);
  return match ? `@${match[1]}` : '';
}

function formatFundingAmount_(amount, currency = '$') {
  if (!amount) return '';
  if (amount >= 1000000000) return `${currency}${(amount / 1000000000).toFixed(1)}B`;
  if (amount >= 1000000) return `${currency}${(amount / 1000000).toFixed(1)}M`;
  if (amount >= 1000) return `${currency}${(amount / 1000).toFixed(1)}K`;
  return `${currency}${amount}`;
}

// NEW: Debug and logging functions
function viewDebugLogs() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const logSheet = ss.getSheetByName('🔍 Debug Logs');
  if (logSheet) {
    ss.setActiveSheet(logSheet);
    SpreadsheetApp.getUi().alert('Debug logs are now visible. Check the "🔍 Debug Logs" sheet.');
  } else {
    SpreadsheetApp.getUi().alert('No debug logs found. Logs will be created when you run "Fetch Company Data".');
  }
}

function clearLogs() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const logSheet = ss.getSheetByName('🔍 Debug Logs');
  if (logSheet) {
    logSheet.clear();
    logSheet.appendRow(['Timestamp', 'Level', 'Component', 'Message', 'Data']);
    logSheet.getRange(1, 1, 1, 5).setFontWeight('bold').setBackground('#34495e').setFontColor('#fff');
    SpreadsheetApp.getUi().alert('Debug logs cleared successfully.');
    logger.info('SYSTEM', 'Debug logs cleared by user');
  } else {
    SpreadsheetApp.getUi().alert('No debug logs to clear.');
  }
}

// ====================== SETUP SHEET ===========================
function setupSheet(){ const sh=SpreadsheetApp.getActiveSheet(); sh.clear(); sh.appendRow(['Company Domain / Website','Status','Error','Last Updated','Results']); sh.getRange(1,1,1,5).setFontWeight('bold').setBackground('#f3f3f3').setHorizontalAlignment('center'); [200,100,200,150,150].forEach((w,i)=>sh.setColumnWidth(i+1,w)); sh.getRange(2,1).setValue('example.com'); toast_('Sheet ready – add domains in column A and run "Fetch Company Data"'); }

// =========================== HELP =============================
function showHelp(){ SpreadsheetApp.getUi().alert(`Apollo Data Fetcher usage:\n\n1. Run "Set / Update API Key" with your Apollo key.\n2. Use "Setup Sheet" to create headers.\n3. Put company domains in column A.\n4. Run "Fetch Company Data" – batches of ${CONFIG.BATCH_SIZE} rows run every 30 s.\n\nStop anytime with "Stop All Jobs".`); }

// =========================== ENHANCED FEATURES =============================
// Additional functions to maximize Apollo.io data extraction

// Enhanced function to extract comprehensive Twitter metrics
function extractTwitterMetrics_(company) {
  const metrics = {
    handle: '',
    url: company.twitter_url || '',
    followers: 'N/A',
    data: null
  };

  if (company.twitter_url) {
    metrics.handle = extractTwitterHandle_(company.twitter_url);

    // Get live Twitter data using TwitterAPI.io
    const username = twitterClient.extractUsernameFromUrl(company.twitter_url);
    if (username) {
      const twitterData = twitterClient.getUserInfo(username);
      if (twitterData) {
        metrics.followers = twitterClient.formatFollowerCount(twitterData.followers);
        metrics.data = twitterData;
        logger.info('TWITTER_METRICS', `Live Twitter data retrieved for ${username}`, {
          followers: twitterData.followers,
          verified: twitterData.verified
        });
      }
    }
  }

  return metrics;
}

// Function to analyze company growth stage based on Apollo data
function analyzeCompanyStage_(company) {
  const employees = company.estimated_num_employees || 0;
  const funding = company.total_funding || 0;
  const founded = company.founded_year || new Date().getFullYear();
  const age = new Date().getFullYear() - founded;

  let stage = 'Unknown';

  if (employees < 10 && funding < 1000000) stage = 'Startup';
  else if (employees < 50 && funding < 10000000) stage = 'Early Stage';
  else if (employees < 200 && funding < 50000000) stage = 'Growth Stage';
  else if (employees < 1000) stage = 'Scale-up';
  else stage = 'Enterprise';

  logger.debug('COMPANY_ANALYSIS', `Company stage analysis for ${company.name}`, {
    stage, employees, funding, age
  });

  return {
    stage,
    employees,
    funding: formatFundingAmount_(funding),
    age,
    riskLevel: age < 2 ? 'High' : age < 5 ? 'Medium' : 'Low'
  };
}

// Function to score lead quality based on Apollo data
function scoreLeadQuality_(company, executives) {
  let score = 0;
  const factors = [];

  // Company size factor
  const employees = company.estimated_num_employees || 0;
  if (employees > 100) { score += 20; factors.push('Large company'); }
  else if (employees > 50) { score += 15; factors.push('Medium company'); }
  else if (employees > 10) { score += 10; factors.push('Small company'); }

  // Funding factor
  if (company.total_funding > 10000000) { score += 25; factors.push('Well funded'); }
  else if (company.total_funding > 1000000) { score += 15; factors.push('Some funding'); }

  // Technology factor
  if (company.technology_names && company.technology_names.length > 10) {
    score += 15; factors.push('Tech-forward');
  }

  // Executive accessibility factor
  const execsWithEmail = executives.filter(e => e.email).length;
  if (execsWithEmail > 0) { score += 20; factors.push('Contactable executives'); }

  // Social presence factor
  if (company.linkedin_url && company.twitter_url) {
    score += 10; factors.push('Strong social presence');
  }

  // Industry factor (add specific industries you target)
  const targetIndustries = ['software', 'technology', 'saas', 'fintech'];
  if (targetIndustries.some(ind => (company.industry || '').toLowerCase().includes(ind))) {
    score += 15; factors.push('Target industry');
  }

  const quality = score >= 70 ? 'High' : score >= 40 ? 'Medium' : 'Low';

  logger.info('LEAD_SCORING', `Lead quality score for ${company.name}`, {
    score, quality, factors
  });

  return { score, quality, factors };
}
// =========================== TWITTER INTEGRATION SUMMARY =============================
/*
🐦 TWITTER INTEGRATION FEATURES:

✅ IMPLEMENTED:
• TwitterAPI.io client integration
• Live follower count fetching
• Twitter verification status
• Engagement metrics calculation
• Account age and creation date
• Tweet count and following metrics
• Enhanced summary with Twitter data
• Professional Twitter analytics section in company sheets

🔧 SETUP REQUIRED:
1. Get API key from https://twitterapi.io
2. Run "Set Social Media APIs" from menu
3. Enter your TwitterAPI.io key
4. Cost: $0.18 per 1K profiles (very affordable)

📊 DATA EXTRACTED:
• Follower count (formatted: 1.2K, 5.4M, etc.)
• Following count
• Tweet count
• Verification status (blue checkmark)
• Account creation date and age
• Location (if available)
• Engagement ratio calculations

💡 BENEFITS:
• Identify high-influence companies
• Assess social media presence
• Validate company legitimacy
• Prioritize outreach based on social metrics
• Track competitor social performance

The integration is seamless and adds significant value to your Apollo.io data!
*/