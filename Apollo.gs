// =============================================================
// Apollo.io Enhanced Company & C‑Level Executive Data Fetcher v5.0.0
// Enhanced with social media metrics, robust logging, and data enrichment
// =============================================================

// ===================== CONFIGURATION ==========================
const CONFIG = {
  BATCH_SIZE: 8,                   // rows per execution (reduced for enhanced processing)
  MAX_RETRIES: 3,                  // for 5xx / 429
  RETRY_DELAY: 1000,               // base ms (exp back‑off)
  RATE_LIMIT_PER_MINUTE: 180,      // Apollo hard cap – conservative limit
  SOCIAL_MEDIA_TIMEOUT: 5000,      // timeout for social media API calls
  LOG_LEVEL: 'INFO',               // DEBUG, INFO, WARN, ERROR
  ENABLE_SOCIAL_METRICS: true,     // enable Twitter/social media metrics
  ENABLE_DATA_ENRICHMENT: true     // enable additional data enrichment
};

const STATUS = {                   // one source of truth
  PENDING:     '',
  PROCESSING:  'Processing',
  COMPLETE:    'Completed',
  ERROR:       'Error',
  ENRICHING:   'Enriching'
};

// ===================== LOGGING SYSTEM ==========================
class Logger {
  constructor(level = CONFIG.LOG_LEVEL) {
    this.levels = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
    this.level = this.levels[level] || 1;
    this.logSheet = null;
  }

  _getLogSheet() {
    if (!this.logSheet) {
      const ss = SpreadsheetApp.getActiveSpreadsheet();
      this.logSheet = ss.getSheetByName('🔍 Debug Logs');
      if (!this.logSheet) {
        this.logSheet = ss.insertSheet('🔍 Debug Logs');
        this.logSheet.appendRow(['Timestamp', 'Level', 'Component', 'Message', 'Data']);
        this.logSheet.getRange(1, 1, 1, 5).setFontWeight('bold').setBackground('#34495e').setFontColor('#fff');
      }
    }
    return this.logSheet;
  }

  _log(level, component, message, data = null) {
    if (this.levels[level] < this.level) return;

    console.log(`[${level}] ${component}: ${message}`, data || '');

    try {
      const sheet = this._getLogSheet();
      const timestamp = new Date().toLocaleString();
      const dataStr = data ? JSON.stringify(data).substring(0, 500) : '';
      sheet.appendRow([timestamp, level, component, message, dataStr]);

      // Keep only last 1000 log entries
      if (sheet.getLastRow() > 1001) {
        sheet.deleteRows(2, sheet.getLastRow() - 1001);
      }
    } catch (err) {
      console.error('Logging failed:', err);
    }
  }

  debug(component, message, data) { this._log('DEBUG', component, message, data); }
  info(component, message, data) { this._log('INFO', component, message, data); }
  warn(component, message, data) { this._log('WARN', component, message, data); }
  error(component, message, data) { this._log('ERROR', component, message, data); }
}

const logger = new Logger();

// =============================================================
//                 ———  MENU  ———
// =============================================================
function onOpen() {
  SpreadsheetApp.getUi().createMenu('🚀 Apollo Enhanced Fetcher')
    .addItem('📋 Setup Sheet',          'setupSheet')
    .addItem('🔍 Fetch Company Data',   'fetchCompanyData')
    .addSeparator()
    .addItem('🔑 Set Apollo API Key',   'setApolloApiKey')
    .addItem('� Set Social Media APIs', 'setSocialMediaKeys')
    .addItem('�🛑 Stop All Jobs',        'stopAllJobs')
    .addSeparator()
    .addItem('📊 View Debug Logs',      'viewDebugLogs')
    .addItem('🧹 Clear Logs',           'clearLogs')
    .addItem('❓ Help',                 'showHelp')
    .addToUi();
}

// ================== API KEY MANAGEMENT ====================
function setApolloApiKey() {
  const ui  = SpreadsheetApp.getUi();
  const res = ui.prompt('Apollo API Key', 'Paste your Apollo.io key:', ui.ButtonSet.OK_CANCEL);
  if (res.getSelectedButton() !== ui.Button.OK) return;
  const key = res.getResponseText().trim();
  if (!key) return;
  PropertiesService.getScriptProperties().setProperty('APOLLO_API_KEY', key);
  logger.info('API_SETUP', 'Apollo API key configured successfully');
  ui.alert('Apollo API key saved ✔️');
}

function setSocialMediaKeys() {
  const ui = SpreadsheetApp.getUi();
  const html = HtmlService.createHtmlOutput(`
    <div style="font-family: Arial, sans-serif; padding: 20px;">
      <h3>Social Media API Configuration</h3>
      <p><strong>Note:</strong> These APIs are optional but enhance data with follower counts and social metrics.</p>

      <h4>Available Options:</h4>
      <ul>
        <li><strong>SocialData.tools:</strong> Free tier available, good for basic Twitter metrics</li>
        <li><strong>TwitterAPI.io:</strong> Pay-as-you-go, comprehensive Twitter data</li>
        <li><strong>RapidAPI Social Media:</strong> Multiple providers, various pricing</li>
      </ul>

      <p>For now, the system will attempt to extract follower counts from public profiles when possible.</p>
      <p>Future versions will support direct API integration.</p>

      <button onclick="google.script.host.close()">OK</button>
    </div>
  `).setWidth(500).setHeight(400);

  ui.showModalDialog(html, 'Social Media API Setup');
  logger.info('API_SETUP', 'Social media API configuration dialog shown');
}

// ==================== PUBLIC ENTRY ============================
function fetchCompanyData() {
  const lock = LockService.getScriptLock();
  if (!lock.tryLock(0)) { toast_('Fetcher is already running.'); return; }
  try {
    const apiKey = PropertiesService.getScriptProperties().getProperty('APOLLO_API_KEY');
    if (!apiKey) { toast_('Missing API key – run "Set / Update API Key" first.'); return; }
    stopAllJobs();                       // kill orphan triggers
    PropertiesService.getScriptProperties().deleteProperty('rowPointer');
    processCompanies_();                 // start immediately
  } finally { lock.releaseLock(); }
}

// =============== BATCH PROCESSOR (PRIVATE) ====================
function processCompanies_() {
  const lock = LockService.getScriptLock();
  if (!lock.tryLock(0)) return;          // another batch is running
  try {
    const ss     = SpreadsheetApp.getActiveSpreadsheet();
    const sheet  = ss.getActiveSheet();
    const rows   = sheet.getLastRow() - 1;  // data rows after header
    if (rows <= 0) { toast_('No company domains to process.'); return; }

    const data   = sheet.getRange(2, 1, rows, 5).getValues();
    const done   = getProcessedDomains_();

    const ptr = Number(PropertiesService.getScriptProperties().getProperty('rowPointer') || 0);
    if (ptr >= data.length) { toast_('All companies processed ✔️'); return; }

    const batch  = data.slice(ptr, ptr + CONFIG.BATCH_SIZE);
    const apollo = new EnhancedApolloClient();

    batch.forEach((row, i) => {
      const [domain, status] = row;
      if (!domain) return;

      // Skip if already complete or summary already has domain
      if (status === STATUS.COMPLETE || done.has(domain)) {
        data[ptr+i][1] = STATUS.COMPLETE;
        return;
      }

      data[ptr+i][1] = STATUS.PROCESSING;
      try {
        const company    = apollo.enrichCompany(domain);
        const executives = apollo.searchExecutives(domain);

        const sheetName  = createOrUpdateCompanySheet_(domain, company, executives);
        updateSummary_(domain, company, executives.length, sheetName);
        done.add(domain);

        data[ptr+i][1] = STATUS.COMPLETE;
        data[ptr+i][4] = `${executives.length} C-level executives`;
      } catch (err) {
        data[ptr+i][1] = STATUS.ERROR;
        data[ptr+i][2] = (err && err.message) || String(err);
      }
    });

    sheet.getRange(2, 1, rows, 5).setValues(data);

    if (ptr + CONFIG.BATCH_SIZE < data.length) {
      PropertiesService.getScriptProperties().setProperty('rowPointer', ptr + CONFIG.BATCH_SIZE);
      ScriptApp.newTrigger('processCompanies_').timeBased().after(30000).create();
    } else {
      PropertiesService.getScriptProperties().deleteProperty('rowPointer');
      toast_('All companies processed ✔️');
    }
  } finally { lock.releaseLock(); }
}

// ===================== APOLLO CLIENT ==========================
class EnhancedApolloClient {
  constructor() {
    this.baseUrl = 'https://api.apollo.io/api/v1/';
    this.apiKey  = PropertiesService.getScriptProperties().getProperty('APOLLO_API_KEY');
    this.rateLimiter = new ApolloRateLimiter();
  }
  enrichCompany(domain) {
    const resp = this.rateLimiter.makeRequest(this.baseUrl+'organizations/enrich', this._opts_({ domain }));
    this._check_(resp);
    return JSON.parse(resp.getContentText()).organization || {};
  }
  searchExecutives(domain) {
    const execs = [];
    let page = 1, total = 1;
    while (page <= total) {
      const resp = this.rateLimiter.makeRequest(
        this.baseUrl+'mixed_people/search',
        this._opts_({ q_organization_domains: domain, person_seniorities:['c_suite','owner','founder'], page, per_page:100 })
      );
      this._check_(resp);
      const j = JSON.parse(resp.getContentText());
      if (Array.isArray(j.people)) execs.push(...j.people.filter(isCLevel_));
      total = j.pagination?.total_pages || 1;
      page++;
    }
    return execs;
  }
  _opts_(payload){ return { method:'post', payload:JSON.stringify(payload), headers:{'X-Api-Key':this.apiKey,'Content-Type':'application/json'}, muteHttpExceptions:true }; }
  _check_(r){ const c=r.getResponseCode(); if(c<200||c>=300) throw new Error(`Apollo ${c}: ${r.getContentText()}`);} }

// ====================== RATE LIMITER ==========================
class ApolloRateLimiter {
  constructor() {
    this.key='apollo_rl';
    const c = CacheService.getScriptCache().get(this.key);
    if(c){ const { windowStart, requestCount } = JSON.parse(c); this.windowStart=windowStart; this.requestCount=requestCount; }
    else { this.windowStart=Date.now(); this.requestCount=0; }
    this.windowDuration=60000; this.maxRequests=CONFIG.RATE_LIMIT_PER_MINUTE;
  }
  makeRequest(url,opt,a=0){ this._gate_(); const r=UrlFetchApp.fetch(url,opt); this.requestCount++; this._save_(); const code=r.getResponseCode(); if([429,500,502,503,504].includes(code)){ if(a>=CONFIG.MAX_RETRIES) return r; Utilities.sleep(Math.min(CONFIG.RETRY_DELAY*2**a,30000)); return this.makeRequest(url,opt,a+1);} return r; }
  _gate_(){ const n=Date.now(); if(n-this.windowStart>=this.windowDuration){ this.windowStart=n; this.requestCount=0;} if(this.requestCount>=this.maxRequests){ Utilities.sleep(this.windowDuration-(n-this.windowStart)); this.windowStart=Date.now(); this.requestCount=0; } }
  _save_(){ CacheService.getScriptCache().put(this.key,JSON.stringify({windowStart:this.windowStart,requestCount:this.requestCount}),60);} }

// ================= EXECUTIVE FILTER HELPER ====================
function isCLevel_(p){ const t=(p.title||'').toLowerCase(); const s=(p.seniority||'').toLowerCase(); const chief=['chief','ceo','cfo','coo','cto','cmo','cro','cpo','chro','cio','cso','cdo','clo'].some(k=>t.includes(k)); const founder=t.includes('founder')||t.includes('owner')||['founder','owner'].includes(s); const vp=t.includes('vice president')||/\bvp\b/.test(t); const pres=t.includes('president')&&!chief; return (chief||founder)&&!vp&&!pres; }

// ============= SHEET CREATION / SUMMARY UPDATE ===============
function createOrUpdateCompanySheet_(domain, company, executives){
  const ss=SpreadsheetApp.getActiveSpreadsheet();
  const sheetName=createValidSheetName_(company.name||domain,ss);
  let sheet=ss.getSheetByName(sheetName); if(!sheet) sheet=ss.insertSheet(sheetName); sheet.clear();

  const rows=[];
  rows.push(['COMPANY INFORMATION']);
  rows.push(['Field','Value']);
  rows.push(['Company Name', company.name||'']);
  rows.push(['Domain',domain]);
  rows.push(['Industry',company.industry||'']);
  rows.push(['Employee Count',company.estimated_num_employees||'']);
  rows.push(['Founded',company.founded_year||'']);
  rows.push(['Headquarters',formatHeadquarters_(company)]);
  rows.push(['Description',company.short_description||company.description||'']);
  rows.push(['Data Fetched',new Date().toLocaleString()]);
  rows.push(['']);
  rows.push(['SOCIAL MEDIA LINKS']);
  rows.push(['Platform','URL']);
  const socials=extractSocialLinks_(company);
  rows.push(['Website',makeClickableLink_(company.website_url||'')]);
  rows.push(['LinkedIn',makeClickableLink_(company.linkedin_url||socials.linkedin)]);
  rows.push(['Twitter',makeClickableLink_(socials.twitter)]);
  rows.push(['Facebook',makeClickableLink_(socials.facebook)]);
  rows.push(['Instagram',makeClickableLink_(socials.instagram)]);
  rows.push(['YouTube',makeClickableLink_(socials.youtube)]);
  socials.others.forEach((u,i)=>rows.push([`Other ${i+1}`,makeClickableLink_(u)]));
  rows.push(['']);
  rows.push([`C‑LEVEL EXECUTIVES (${executives.length} found)`]);
  rows.push(['Name','Title','Email','LinkedIn','Phone','Location']);
  const prio={ceo:1,coo:2,cfo:3,cto:4,cmo:5,cro:6,founder:7,owner:8};
  executives.sort((a,b)=>{const ak=Object.keys(prio).find(k=>(a.title||'').toLowerCase().includes(k))||'zz';const bk=Object.keys(prio).find(k=>(b.title||'').toLowerCase().includes(k))||'zz';return (prio[ak]||99)-(prio[bk]||99);});
  executives.forEach(e=>rows.push([e.name||`${e.first_name||''} ${e.last_name||''}`.trim(),e.title||'',makeClickableEmail_(e.email),makeClickableLink_(e.linkedin_url),(e.phone_numbers||[]).map(p=>p.raw_number).join(', '),[e.city,e.state,e.country].filter(Boolean).join(', ')]));
  sheet.getRange(1,1,rows.length,6).setValues(rows.map(r=>{while(r.length<6)r.push('');return r;}));
  [150,200,200,200,150,150].forEach((w,i)=>sheet.setColumnWidth(i+1,w));
  sheet.getRange(1,1).setFontSize(14).setFontWeight('bold');
  return sheetName;                                  // <- return for Summary link
}

function updateSummary_(domain, company, execCount, sheetName){
  const ss=SpreadsheetApp.getActiveSpreadsheet();
  let summary=ss.getSheetByName('📊 Summary');
  if(!summary){ summary=ss.insertSheet('📊 Summary'); summary.appendRow(['Company','Domain','C‑level Count','Industry','Employees','Headquarters','Last Updated','Sheet Link']); summary.getRange(1,1,1,8).setFontWeight('bold').setBackground('#1a73e8').setFontColor('#fff'); }
  const last=summary.getLastRow();
  let domains=[]; if(last>=2) domains=summary.getRange(2,2,last-1,1).getValues().flat();
  const idx=domains.findIndex(d=>d===domain);
  const linkSheet=ss.getSheetByName(sheetName);
  const sheetLink=linkSheet?`=HYPERLINK("#gid=${linkSheet.getSheetId()}","View →")`:'';
  const row=[company.name||domain,domain,execCount,company.industry||'',company.estimated_num_employees||'',formatHeadquarters_(company),new Date().toLocaleString(),sheetLink];
  if(idx>=0) summary.getRange(idx+2,1,1,row.length).setValues([row]);
  else summary.appendRow(row);
}

// =============== UTIL & HELPER FUNCTIONS =====================
function getProcessedDomains_(){ const ss=SpreadsheetApp.getActiveSpreadsheet(); const s=ss.getSheetByName('📊 Summary'); if(!s||s.getLastRow()<2) return new Set(); return new Set(s.getRange(2,2,s.getLastRow()-1,1).getValues().flat().filter(Boolean)); }
function stopAllJobs(){ ScriptApp.getProjectTriggers().filter(t=>t.getHandlerFunction()==='processCompanies_').forEach(t=>ScriptApp.deleteTrigger(t)); }
function createValidSheetName_(name,ss){ let base=name.replace(/[*/\[\]:?]/g,'').substring(0,50).trim()||'Sheet'; let finalName=base, idx=1; while(ss.getSheetByName(finalName)){ finalName=`${base} (${idx++})`; } return finalName; }
function extractSocialLinks_(c){ const r={linkedin:'',twitter:'',facebook:'',instagram:'',youtube:'',others:[]}; const urls=[c.linkedin_url,c.twitter_url,c.facebook_url,...(c.social_urls||[])].filter(Boolean); urls.forEach(u=>{ if(u.includes('linkedin.com')) r.linkedin=u; else if(u.includes('twitter.com')) r.twitter=u; else if(u.includes('facebook.com')) r.facebook=u; else if(u.includes('instagram.com')) r.instagram=u; else if(u.includes('youtube.com')) r.youtube=u; else r.others.push(u); }); return r; }
function formatHeadquarters_(c){ if(c.primary_address){ const a=c.primary_address; return [a.city,a.state,a.country].filter(Boolean).join(', ');} return [c.city,c.state,c.country].filter(Boolean).join(', ');}  
function makeClickableLink_(url){ if(!url) return ''; if(!/^https?:\/\//i.test(url)) url='https://'+url; return `=HYPERLINK("${url}","View →")`; }
function makeClickableEmail_(email){ if(!email) return ''; return `=HYPERLINK("mailto:${email}","${email}")`; }
function toast_(msg){ SpreadsheetApp.getActiveSpreadsheet().toast(msg,'Apollo'); }

// ====================== SETUP SHEET ===========================
function setupSheet(){ const sh=SpreadsheetApp.getActiveSheet(); sh.clear(); sh.appendRow(['Company Domain / Website','Status','Error','Last Updated','Results']); sh.getRange(1,1,1,5).setFontWeight('bold').setBackground('#f3f3f3').setHorizontalAlignment('center'); [200,100,200,150,150].forEach((w,i)=>sh.setColumnWidth(i+1,w)); sh.getRange(2,1).setValue('example.com'); toast_('Sheet ready – add domains in column A and run "Fetch Company Data"'); }

// =========================== HELP =============================
function showHelp(){ SpreadsheetApp.getUi().alert(`Apollo Data Fetcher usage:\n\n1. Run "Set / Update API Key" with your Apollo key.\n2. Use "Setup Sheet" to create headers.\n3. Put company domains in column A.\n4. Run "Fetch Company Data" – batches of ${CONFIG.BATCH_SIZE} rows run every 30 s.\n\nStop anytime with "Stop All Jobs".`); }